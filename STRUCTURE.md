# Estrutura do Projeto Gatewayfy

Este documento descreve a estrutura de pastas e arquivos criada seguindo o padrão do Next.js App Router.

## Estrutura de Pastas

```
src/
├── app/                          # App Router (Next.js 13+)
│   ├── dashboard/               # Dashboard principal
│   │   ├── page.tsx            # Página do dashboard
│   │   ├── products/           # Gerenciamento de produtos
│   │   │   └── page.tsx
│   │   └── orders/             # Gerenciamento de pedidos
│   │       └── page.tsx
│   ├── buy/                    # Processo de compra
│   │   └── [productId]/        # Rota dinâmica para produto específico
│   │       └── page.tsx
│   ├── order/                  # Visualização de pedidos
│   │   └── [orderId]/          # Rota dinâmica para pedido específico
│   │       └── page.tsx
│   ├── api/                    # API Routes
│   │   ├── webhooks/           # Webhooks
│   │   │   └── payment.ts      # Webhook de pagamento
│   │   └── checkout/           # Processo de checkout
│   │       └── route.ts
│   ├── layout.tsx              # Layout principal
│   ├── page.tsx                # Página inicial
│   ├── globals.css             # Estilos globais
│   └── favicon.ico             # Ícone do site
├── lib/                        # Bibliotecas e utilitários
│   ├── auth.ts                 # Configuração de autenticação
│   └── prisma.ts               # Cliente Prisma
├── components/                 # Componentes React
│   ├── ui/                     # Componentes de UI básicos
│   │   ├── button.tsx          # Componente Button
│   │   ├── input.tsx           # Componente Input
│   │   └── index.ts            # Exports dos componentes UI
│   ├── forms/                  # Formulários
│   │   ├── checkout-form.tsx   # Formulário de checkout
│   │   └── index.ts            # Exports dos formulários
│   ├── product-card.tsx        # Card de produto
│   └── index.ts                # Exports principais
├── constants/                  # Constantes da aplicação
│   └── index.ts                # Constantes gerais
├── utils/                      # Funções utilitárias
│   └── index.ts                # Utilitários gerais
└── types/                      # Definições de tipos TypeScript
    └── index.ts                # Tipos principais
```

## Rotas Criadas

### Páginas
- `/` - Página inicial
- `/dashboard` - Dashboard principal
- `/dashboard/products` - Gerenciamento de produtos
- `/dashboard/orders` - Gerenciamento de pedidos
- `/buy/[productId]` - Página de compra de produto específico
- `/order/[orderId]` - Visualização de pedido específico

### API Routes
- `POST /api/checkout` - Processar checkout
- `GET /api/checkout` - Informações do checkout
- `POST /api/webhooks/payment` - Webhook de pagamento

## Componentes Criados

### UI Components
- **Button**: Componente de botão reutilizável com variantes
- **Input**: Componente de input com label e validação

### Forms
- **CheckoutForm**: Formulário completo de checkout com informações de contato, endereço e pagamento

### Product
- **ProductCard**: Card para exibição de produtos com botão de compra

## Bibliotecas

### Auth (`lib/auth.ts`)
- Configuração de autenticação
- Funções para login, logout e verificação de usuário

### Prisma (`lib/prisma.ts`)
- Cliente Prisma configurado para desenvolvimento e produção
- Singleton pattern para evitar múltiplas conexões

## Tipos TypeScript

Definições completas para:
- User, Product, Order, Payment
- Enums para status de pedidos e pagamentos
- Tipos para formulários e respostas da API
- Interfaces para paginação

## Utilitários

Funções auxiliares para:
- Formatação de moeda e datas
- Validação de email
- Debounce e sleep
- Manipulação de strings

## Constantes

- Endpoints da API
- Status de pagamentos e pedidos
- Rotas da aplicação
- Configurações gerais

## Próximos Passos

1. Configurar Prisma com schema do banco de dados
2. Implementar autenticação completa
3. Adicionar validação de formulários
4. Configurar processamento de pagamentos
5. Implementar testes unitários
6. Adicionar middleware de autenticação
